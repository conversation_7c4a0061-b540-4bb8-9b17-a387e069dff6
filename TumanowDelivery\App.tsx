import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, ActivityIndicator } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider as PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Import screens
import WelcomeScreen from './src/screens/WelcomeScreen';
import LoginScreen from './src/screens/LoginScreen';
import ShopOwnerDashboard from './src/screens/ShopOwnerDashboard';
import ClientDashboard from './src/screens/ClientDashboard';
import BikerDashboard from './src/screens/BikerDashboard';

// Import theme and context
import { theme } from './src/theme/colors';
import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import { colors, commonStyles } from './src/theme';

const Stack = createStackNavigator();

// Loading component
const LoadingScreen = () => (
  <View style={commonStyles.centeredContainer}>
    <ActivityIndicator size="large" color={colors.primary} />
    <Text style={{ marginTop: 16, color: colors.textSecondary }}>Loading...</Text>
  </View>
);

// Navigation component that uses auth state
const AppNavigator = () => {
  const { user, userProfile, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: theme.colors.primary,
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        {user && userProfile ? (
          // User is authenticated - show appropriate dashboard
          <>
            {userProfile.role === 'shop_owner' && (
              <Stack.Screen
                name="ShopOwnerDashboard"
                component={ShopOwnerDashboard}
                options={{ title: 'Shop Dashboard', headerLeft: () => null }}
              />
            )}
            {userProfile.role === 'client' && (
              <Stack.Screen
                name="ClientDashboard"
                component={ClientDashboard}
                options={{ title: 'My Orders', headerLeft: () => null }}
              />
            )}
            {userProfile.role === 'biker' && (
              <Stack.Screen
                name="BikerDashboard"
                component={BikerDashboard}
                options={{ title: 'Delivery Dashboard', headerLeft: () => null }}
              />
            )}
          </>
        ) : (
          // User is not authenticated - show auth flow
          <>
            <Stack.Screen
              name="Welcome"
              component={WelcomeScreen}
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{ title: 'Login to Tumanow' }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default function App() {
  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AuthProvider>
          <AppNavigator />
        </AuthProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
}
