{"version": 3, "file": "NativeStatusBarWrapper.d.ts", "sourceRoot": "", "sources": ["../src/NativeStatusBarWrapper.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAKL,KAAK,UAAU,EAChB,MAAM,cAAc,CAAC;AAEtB,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAE7E;;;;;;;;;GASG;AACH,wBAAgB,SAAS,CAAC,EACxB,KAAK,EACL,uBAAuB,EACvB,WAAkB,EAClB,eAAe,EAAE,mBAAmB,EACpC,GAAG,KAAK,EACT,EAAE,cAAc,qBAsBhB;AAGD;;;;GAIG;AACH,wBAAgB,iBAAiB,CAAC,KAAK,EAAE,cAAc,EAAE,QAAQ,CAAC,EAAE,OAAO,QAE1E;AAGD;;;;GAIG;AACH,wBAAgB,kBAAkB,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,kBAAkB,QAEjF;AAGD;;;;;GAKG;AACH,wBAAgB,2BAA2B,CAAC,eAAe,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,OAAO,QAE1F;AAGD;;;;GAIG;AACH,wBAAgB,2CAA2C,CAAC,OAAO,EAAE,OAAO,QAE3E;AAGD;;;;;GAKG;AACH,wBAAgB,uBAAuB,CAAC,WAAW,EAAE,OAAO,QAE3D"}