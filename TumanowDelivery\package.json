{"name": "tumanow-delivery", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.20", "react": "19.0.0", "react-native": "0.79.5", "@expo/vector-icons": "^14.0.4", "react-native-screens": "~4.11.1", "react-native-safe-area-context": "5.4.0", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@supabase/supabase-js": "^2.45.0", "@react-native-async-storage/async-storage": "2.1.2", "react-native-url-polyfill": "^2.0.0", "expo-location": "~18.1.6", "expo-notifications": "~0.31.4", "expo-constants": "~17.1.7", "expo-device": "~7.1.4", "react-native-maps": "1.20.1", "expo-linear-gradient": "~14.1.5", "react-native-paper": "^5.12.5", "react-native-vector-icons": "^10.2.0", "expo-status-bar": "~2.2.3"}, "devDependencies": {"@babel/core": "^7.25.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}