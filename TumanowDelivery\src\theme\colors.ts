import { DefaultTheme } from 'react-native-paper';

// Tumanow App Color Scheme - Delivery Classic (Bright + Trustworthy)
export const colors = {
  primary: '#FF6600',      // Safety Orange - Brand & button color
  primaryDark: '#E55A00',  // Darker Safety Orange
  secondary: '#FFFFFF',    // True White
  accent: '#007AFF',       // Cool Blue - Links / tracking screens
  background: '#FFFFFF',   // True White - General background
  surface: '#F4F4F4',      // Light Gray - Backgrounds / cards
  text: '#333333',         // Charcoal - Text / headers
  textSecondary: '#666666', // Medium Charcoal for secondary text
  success: '#28A745',      // Green
  warning: '#FFC107',      // Yellow
  error: '#DC3545',        // Red
  info: '#007AFF',         // Cool Blue
  border: '#E0E0E0',       // Light border
  placeholder: '#999999',  // Medium gray for placeholders
  disabled: '#CCCCCC',     // Light gray for disabled elements
  shadow: '#000000',       // Black for shadows

  // Delivery Status Colors
  pending: '#FFC107',      // Yellow
  accepted: '#007AFF',     // Cool Blue
  inProgress: '#FF6600',   // Safety Orange
  delivered: '#28A745',    // Green
  cancelled: '#DC3545',    // Red

  // User Role Colors
  shopOwner: '#FF6600',    // Safety Orange
  client: '#007AFF',       // Cool Blue
  biker: '#28A745',        // Green
};

// React Native Paper Theme
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.accent,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    disabled: colors.disabled,
    placeholder: colors.placeholder,
    backdrop: 'rgba(0, 0, 0, 0.5)',
    onSurface: colors.text,
    notification: colors.error,
  },
  roundness: 12,
};

// Gradient Colors
export const gradients = {
  primary: [colors.primary, colors.primaryDark],
  secondary: [colors.secondary, colors.surface],
  accent: [colors.accent, '#0056CC'],
  success: [colors.success, '#1E7E34'],
  warning: [colors.warning, '#E0A800'],
  error: [colors.error, '#BD2130'],
};

// Shadow Styles
export const shadows = {
  small: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medium: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6.27,
    elevation: 8,
  },
  large: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 10.32,
    elevation: 12,
  },
};
