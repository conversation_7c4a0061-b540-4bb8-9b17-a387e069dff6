import { DefaultTheme } from 'react-native-paper';

// Tumanow App Color Scheme - Dark Rider (Luxury + Precision)
export const colors = {
  primary: '#DC143C',      // Crimson Red - Action elements
  primaryDark: '#B91C3C',  // Darker Crimson Red
  secondary: '#FAFAFA',    // <PERSON> - Text, contrasts
  accent: '#FFD700',       // Royal Gold - Premium, loyalty features
  background: '#0F0F0F',   // Carbon Black - Background / dark mode
  surface: '#1A1A1A',      // Slightly lighter Carbon Black for cards
  text: '#FAFAFA',         // <PERSON> White - Primary text
  textPrimary: '#FAFAFA',   // <PERSON> White - Primary text (alias)
  textSecondary: '#B0B0B0', // Light gray for secondary text
  success: '#10B981',      // <PERSON> (adjusted for dark theme)
  warning: '#F59E0B',      // Amber (adjusted for dark theme)
  error: '#EF4444',        // Red (adjusted for dark theme)
  info: '#3B82F6',         // Blue (adjusted for dark theme)
  border: '#3C3F41',       // <PERSON> Gray - Dividers, menus
  placeholder: '#6B7280',  // Gray for placeholders
  disabled: '#4B5563',     // Darker gray for disabled elements
  shadow: '#000000',       // Black for shadows

  // Delivery Status Colors
  pending: '#F59E0B',      // Amber
  accepted: '#3B82F6',     // Blue
  inProgress: '#DC143C',   // Crimson Red
  delivered: '#10B981',    // Green
  cancelled: '#EF4444',    // Red

  // User Role Colors
  shopOwner: '#DC143C',    // Crimson Red
  client: '#3B82F6',       // Blue
  biker: '#10B981',        // Green
};

// React Native Paper Theme
export const theme = {
  ...DefaultTheme,
  dark: true,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.accent,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    disabled: colors.disabled,
    placeholder: colors.placeholder,
    backdrop: 'rgba(15, 15, 15, 0.8)',
    onSurface: colors.text,
    notification: colors.error,
    onBackground: colors.text,
    onPrimary: colors.secondary,
  },
  roundness: 12,
};

// Gradient Colors
export const gradients = {
  primary: [colors.primary, colors.primaryDark] as const,
  secondary: [colors.surface, colors.background] as const,
  accent: [colors.accent, '#E6C200'] as const,
  success: [colors.success, '#059669'] as const,
  warning: [colors.warning, '#D97706'] as const,
  error: [colors.error, '#DC2626'] as const,
};

// Shadow Styles
export const shadows = {
  small: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medium: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6.27,
    elevation: 8,
  },
  large: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 10.32,
    elevation: 12,
  },
};
