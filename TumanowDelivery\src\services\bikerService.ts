import { supabase } from './supabase';

export interface BikerLocation {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  rating: number;
  vehicleType: 'motorcycle' | 'bicycle';
  estimatedArrival: string;
  isOnline: boolean;
  lastUpdated: string;
}

export interface DeliveryRequest {
  id: string;
  customerId: string;
  bikerId: string;
  pickupLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  deliveryLocation: {
    latitude: number;
    longitude: number;
    address: string;
  };
  status: 'pending' | 'accepted' | 'in_progress' | 'delivered' | 'cancelled';
  estimatedTime: string;
  createdAt: string;
}

class BikerService {
  private mockBikers: BikerLocation[] = [
    {
      id: '1',
      name: '<PERSON>',
      latitude: -17.8252,
      longitude: 31.0335,
      rating: 4.8,
      vehicleType: 'motorcycle',
      estimatedArrival: '5-8 min',
      isOnline: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: '2',
      name: '<PERSON>',
      latitude: -17.8242,
      longitude: 31.0345,
      rating: 4.9,
      vehicleType: 'bicycle',
      estimatedArrival: '8-12 min',
      isOnline: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: '3',
      name: '<PERSON> <PERSON>.',
      latitude: -17.8262,
      longitude: 31.0325,
      rating: 4.7,
      vehicleType: 'motorcycle',
      estimatedArrival: '6-10 min',
      isOnline: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: '4',
      name: 'Lisa T.',
      latitude: -17.8272,
      longitude: 31.0315,
      rating: 4.6,
      vehicleType: 'bicycle',
      estimatedArrival: '10-15 min',
      isOnline: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: '5',
      name: 'David W.',
      latitude: -17.8232,
      longitude: 31.0355,
      rating: 4.9,
      vehicleType: 'motorcycle',
      estimatedArrival: '4-7 min',
      isOnline: true,
      lastUpdated: new Date().toISOString(),
    },
  ];

  // Get nearby bikers within a radius (in km)
  async getNearbyBikers(
    userLatitude: number,
    userLongitude: number,
    radiusKm: number = 5
  ): Promise<BikerLocation[]> {
    try {
      // In a real app, this would query the database
      // For now, we'll use mock data and simulate distance calculation
      const nearbyBikers = this.mockBikers.filter(biker => {
        const distance = this.calculateDistance(
          userLatitude,
          userLongitude,
          biker.latitude,
          biker.longitude
        );
        return distance <= radiusKm && biker.isOnline;
      });

      // Sort by distance (closest first)
      return nearbyBikers.sort((a, b) => {
        const distanceA = this.calculateDistance(userLatitude, userLongitude, a.latitude, a.longitude);
        const distanceB = this.calculateDistance(userLatitude, userLongitude, b.latitude, b.longitude);
        return distanceA - distanceB;
      });
    } catch (error) {
      console.error('Error fetching nearby bikers:', error);
      return [];
    }
  }

  // Calculate distance between two coordinates using Haversine formula
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  // Calculate estimated arrival time based on distance and vehicle type
  calculateEstimatedArrival(distance: number, vehicleType: 'motorcycle' | 'bicycle'): string {
    // Average speeds: motorcycle = 30 km/h, bicycle = 15 km/h
    const speed = vehicleType === 'motorcycle' ? 30 : 15;
    const timeInHours = distance / speed;
    const timeInMinutes = Math.ceil(timeInHours * 60);
    
    if (timeInMinutes <= 5) {
      return '3-5 min';
    } else if (timeInMinutes <= 10) {
      return '5-10 min';
    } else if (timeInMinutes <= 15) {
      return '10-15 min';
    } else if (timeInMinutes <= 20) {
      return '15-20 min';
    } else {
      return '20+ min';
    }
  }

  // Request delivery from a specific biker
  async requestDelivery(
    bikerId: string,
    pickupLocation: { latitude: number; longitude: number; address: string },
    deliveryLocation: { latitude: number; longitude: number; address: string }
  ): Promise<{ success: boolean; deliveryRequest?: DeliveryRequest; error?: string }> {
    try {
      // In a real app, this would create a record in the database
      const deliveryRequest: DeliveryRequest = {
        id: `delivery_${Date.now()}`,
        customerId: 'current_user_id', // This would come from auth context
        bikerId,
        pickupLocation,
        deliveryLocation,
        status: 'pending',
        estimatedTime: '15-20 min', // This would be calculated based on distance
        createdAt: new Date().toISOString(),
      };

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        deliveryRequest,
      };
    } catch (error) {
      console.error('Error requesting delivery:', error);
      return {
        success: false,
        error: 'Failed to request delivery. Please try again.',
      };
    }
  }

  // Get biker details by ID
  async getBikerById(bikerId: string): Promise<BikerLocation | null> {
    try {
      const biker = this.mockBikers.find(b => b.id === bikerId);
      return biker || null;
    } catch (error) {
      console.error('Error fetching biker details:', error);
      return null;
    }
  }

  // Simulate real-time location updates
  simulateLocationUpdates(callback: (bikers: BikerLocation[]) => void): () => void {
    const interval = setInterval(() => {
      // Slightly move bikers to simulate real movement
      this.mockBikers = this.mockBikers.map(biker => ({
        ...biker,
        latitude: biker.latitude + (Math.random() - 0.5) * 0.001, // Small random movement
        longitude: biker.longitude + (Math.random() - 0.5) * 0.001,
        lastUpdated: new Date().toISOString(),
      }));
      
      callback(this.mockBikers);
    }, 10000); // Update every 10 seconds

    // Return cleanup function
    return () => clearInterval(interval);
  }

  // Get delivery status updates
  async getDeliveryStatus(deliveryId: string): Promise<DeliveryRequest | null> {
    try {
      // In a real app, this would query the database
      // For now, simulate status progression
      return null;
    } catch (error) {
      console.error('Error fetching delivery status:', error);
      return null;
    }
  }
}

export const bikerService = new BikerService();
