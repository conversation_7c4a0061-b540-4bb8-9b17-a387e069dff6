import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing, layout, commonStyles, shadows } from '../theme';
import { useAuth } from '../contexts/AuthContext';

interface ClientDashboardProps {
  navigation: any;
}

export default function ClientDashboard({ navigation }: ClientDashboardProps) {
  const { signOut, userProfile } = useAuth();
  const nearbyShops = [
    { id: '1', name: 'Fresh Mart', category: 'Grocery', distance: '0.5 km', rating: 4.8, deliveryTime: '15-20 min' },
    { id: '2', name: 'Tech Store', category: 'Electronics', distance: '1.2 km', rating: 4.6, deliveryTime: '20-30 min' },
    { id: '3', name: 'Fashion Hub', category: 'Clothing', distance: '0.8 km', rating: 4.9, deliveryTime: '25-35 min' },
  ];

  const activeOrders = [
    { id: '1', shop: 'Fresh Mart', items: '3 items', status: 'in_progress', estimatedTime: '15 min', biker: '<PERSON>.' },
    { id: '2', shop: 'Tech Store', items: '1 item', status: 'pending', estimatedTime: '25 min', biker: null },
  ];

  const categories = [
    { id: '1', name: 'Grocery', icon: 'basket', color: colors.success },
    { id: '2', name: 'Electronics', icon: 'phone-portrait', color: colors.info },
    { id: '3', name: 'Clothing', icon: 'shirt', color: colors.warning },
    { id: '4', name: 'Pharmacy', icon: 'medical', color: colors.error },
    { id: '5', name: 'Food', icon: 'restaurant', color: colors.shopOwner },
    { id: '6', name: 'More', icon: 'grid', color: colors.textSecondary },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return colors.pending;
      case 'in_progress': return colors.inProgress;
      case 'delivered': return colors.delivered;
      default: return colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Finding Rider';
      case 'in_progress': return 'On the Way';
      case 'delivered': return 'Delivered';
      default: return status;
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: signOut },
      ]
    );
  };

  const renderShopCard = ({ item }: { item: any }) => (
    <TouchableOpacity style={styles.shopCard}>
      <View style={styles.shopHeader}>
        <Text style={styles.shopName}>{item.name}</Text>
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={16} color={colors.warning} />
          <Text style={styles.rating}>{item.rating}</Text>
        </View>
      </View>
      <Text style={styles.shopCategory}>{item.category}</Text>
      <View style={styles.shopFooter}>
        <Text style={styles.distance}>{item.distance}</Text>
        <Text style={styles.deliveryTime}>{item.deliveryTime}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Hello!</Text>
            <Text style={styles.userName}>{userProfile?.full_name || 'Welcome back'}</Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.notificationButton}>
              <Ionicons name="notifications" size={24} color={colors.primary} />
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationCount}>2</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <Ionicons name="log-out" size={24} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <TouchableOpacity style={styles.searchBar}>
          <Ionicons name="search" size={20} color={colors.textSecondary} />
          <Text style={styles.searchPlaceholder}>Search shops, products...</Text>
        </TouchableOpacity>

        {/* Active Orders */}
        {activeOrders.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Active Orders</Text>
            {activeOrders.map((order) => (
              <TouchableOpacity key={order.id} style={styles.activeOrderCard}>
                <View style={styles.orderHeader}>
                  <Text style={styles.orderShop}>{order.shop}</Text>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                    <Text style={styles.statusText}>{getStatusText(order.status)}</Text>
                  </View>
                </View>
                <Text style={styles.orderItems}>{order.items}</Text>
                <View style={styles.orderFooter}>
                  <Text style={styles.estimatedTime}>ETA: {order.estimatedTime}</Text>
                  {order.biker && (
                    <Text style={styles.bikerName}>Rider: {order.biker}</Text>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <View style={styles.categoriesContainer}>
            {categories.map((category) => (
              <TouchableOpacity key={category.id} style={styles.categoryCard}>
                <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                  <Ionicons name={category.icon as any} size={24} color={colors.secondary} />
                </View>
                <Text style={styles.categoryName}>{category.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Nearby Shops */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Nearby Shops</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={nearbyShops}
            renderItem={renderShopCard}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.shopsContainer}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  greeting: {
    ...typography.body1,
    color: colors.textSecondary,
  },
  userName: {
    ...typography.h3,
    color: colors.text,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationButton: {
    position: 'relative',
    padding: spacing.xs,
    marginRight: spacing.sm,
  },
  logoutButton: {
    padding: spacing.xs,
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: colors.error,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCount: {
    ...typography.caption,
    color: colors.secondary,
    fontSize: 10,
    fontWeight: 'bold',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: layout.inputRadius,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    marginBottom: spacing.lg,
    ...shadows.small,
  },
  searchPlaceholder: {
    ...typography.body1,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    ...typography.h5,
    color: colors.text,
    fontWeight: '600',
  },
  seeAllText: {
    ...typography.body2,
    color: colors.primary,
    fontWeight: '500',
  },
  activeOrderCard: {
    backgroundColor: colors.surface,
    borderRadius: layout.cardRadius,
    padding: spacing.md,
    marginBottom: spacing.sm,
    ...shadows.small,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  orderShop: {
    ...typography.subtitle1,
    color: colors.text,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: layout.radiusSM,
  },
  statusText: {
    ...typography.caption,
    color: colors.secondary,
    fontWeight: '500',
  },
  orderItems: {
    ...typography.body2,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  estimatedTime: {
    ...typography.body2,
    color: colors.primary,
    fontWeight: '500',
  },
  bikerName: {
    ...typography.body2,
    color: colors.textSecondary,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    alignItems: 'center',
    width: '30%',
    marginBottom: spacing.md,
  },
  categoryIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  categoryName: {
    ...typography.caption,
    color: colors.text,
    textAlign: 'center',
    fontWeight: '500',
  },
  shopsContainer: {
    paddingRight: spacing.lg,
  },
  shopCard: {
    backgroundColor: colors.surface,
    borderRadius: layout.cardRadius,
    padding: spacing.md,
    marginRight: spacing.md,
    width: 200,
    ...shadows.small,
  },
  shopHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  shopName: {
    ...typography.subtitle1,
    color: colors.text,
    fontWeight: '600',
    flex: 1,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    ...typography.caption,
    color: colors.text,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  shopCategory: {
    ...typography.body2,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  shopFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  distance: {
    ...typography.caption,
    color: colors.primary,
    fontWeight: '500',
  },
  deliveryTime: {
    ...typography.caption,
    color: colors.textSecondary,
  },
});
