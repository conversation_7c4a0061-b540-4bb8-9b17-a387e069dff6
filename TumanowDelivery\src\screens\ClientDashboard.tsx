import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';
import { colors, typography, spacing, commonStyles } from '../theme';
import { useAuth } from '../contexts/AuthContext';
import { bikerService } from '../services/bikerService';

interface ClientDashboardProps {
  navigation: any;
}

interface BikerLocation {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  rating: number;
  vehicleType: string;
  estimatedArrival: string;
  isOnline: boolean;
}

interface UserLocation {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}



export default function ClientDashboard({ }: ClientDashboardProps) {
  const { signOut, userProfile } = useAuth();
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [nearbyBikers, setNearbyBikers] = useState<BikerLocation[]>([]);
  const [selectedBiker, setSelectedBiker] = useState<BikerLocation | null>(null);
  const [bottomSheetHeight] = useState(new Animated.Value(120));
  const [isBottomSheetExpanded, setIsBottomSheetExpanded] = useState(false);
  const mapRef = useRef<MapView>(null);



  useEffect(() => {
    requestLocationPermission();
  }, []);

  useEffect(() => {
    if (userLocation) {
      loadNearbyBikers();

      // Set up real-time updates
      const cleanup = bikerService.simulateLocationUpdates(() => {
        // Filter bikers within 5km radius
        bikerService.getNearbyBikers(
          userLocation.latitude,
          userLocation.longitude,
          5
        ).then(setNearbyBikers);
      });

      return cleanup;
    }
  }, [userLocation]);

  const loadNearbyBikers = async () => {
    if (userLocation) {
      const bikers = await bikerService.getNearbyBikers(
        userLocation.latitude,
        userLocation.longitude,
        5 // 5km radius
      );
      setNearbyBikers(bikers);
    }
  };

  const requestLocationPermission = async () => {
    try {
      console.log('Requesting location permission...');
      const { status } = await Location.requestForegroundPermissionsAsync();
      console.log('Location permission status:', status);

      if (status !== 'granted') {
        console.log('Location permission denied, using fallback location');
        Alert.alert('Permission denied', 'Using default location. Grant location permission for better experience.');
        // Use fallback location immediately
        setUserLocation({
          latitude: -17.8252,
          longitude: 31.0335,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
        return;
      }

      console.log('Getting current position...');
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      console.log('Got location:', location.coords);

      const userLoc: UserLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
      setUserLocation(userLoc);
    } catch (error) {
      console.error('Error getting location:', error);
      // Fallback to Harare coordinates
      console.log('Using fallback location due to error');
      setUserLocation({
        latitude: -17.8252,
        longitude: 31.0335,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: signOut },
      ]
    );
  };

  const handleBikerSelect = (biker: BikerLocation) => {
    setSelectedBiker(biker);
    expandBottomSheet();
  };

  const expandBottomSheet = () => {
    setIsBottomSheetExpanded(true);
    Animated.timing(bottomSheetHeight, {
      toValue: 300,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const collapseBottomSheet = () => {
    setIsBottomSheetExpanded(false);
    Animated.timing(bottomSheetHeight, {
      toValue: 120,
      duration: 300,
      useNativeDriver: false,
    }).start();
  };

  const getBikerIcon = (vehicleType: string) => {
    return vehicleType === 'motorcycle' ? 'car-sport' : 'bicycle';
  };

  const handleRequestDelivery = async () => {
    if (selectedBiker && userLocation) {
      Alert.alert(
        'Request Delivery',
        `Request delivery from ${selectedBiker.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Request',
            onPress: async () => {
              try {
                const result = await bikerService.requestDelivery(
                  selectedBiker.id,
                  {
                    latitude: userLocation.latitude,
                    longitude: userLocation.longitude,
                    address: 'Current Location'
                  },
                  {
                    latitude: userLocation.latitude + 0.01, // Mock delivery location
                    longitude: userLocation.longitude + 0.01,
                    address: 'Delivery Location'
                  }
                );

                if (result.success) {
                  Alert.alert('Success', 'Delivery request sent! The rider will contact you shortly.');
                } else {
                  Alert.alert('Error', result.error || 'Failed to request delivery');
                }
              } catch (error) {
                Alert.alert('Error', 'Something went wrong. Please try again.');
              }

              collapseBottomSheet();
              setSelectedBiker(null);
            }
          },
        ]
      );
    }
  };

  if (!userLocation) {
    return (
      <SafeAreaView style={commonStyles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading map...</Text>
          <Text style={styles.loadingText}>
            {userLocation ? 'Location loaded' : 'Getting location...'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={commonStyles.container}>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={styles.greeting}>Hello!</Text>
            <Text style={styles.userName}>{userProfile?.full_name || 'Welcome back'}</Text>
          </View>
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.notificationButton}>
              <Ionicons name="notifications" size={24} color={colors.primary} />
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationCount}>2</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <Ionicons name="log-out" size={24} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Map */}
        <View style={styles.mapContainer}>
          {/* Debug Info */}
          <View style={styles.debugOverlay}>
            <Text style={styles.debugText}>
              Location: {userLocation.latitude.toFixed(4)}, {userLocation.longitude.toFixed(4)}
            </Text>
            <Text style={styles.debugText}>
              Bikers: {nearbyBikers.length} available
            </Text>
          </View>

          {/* Simple List View as Map Alternative */}
          <View style={styles.mapAlternative}>
            <View style={styles.mapHeader}>
              <Ionicons name="map" size={32} color={colors.primary} />
              <Text style={styles.mapTitle}>Nearby Riders</Text>
            </View>

            <ScrollView style={styles.ridersList} showsVerticalScrollIndicator={false}>
              {nearbyBikers.map((biker) => (
                <TouchableOpacity
                  key={biker.id}
                  style={[
                    styles.riderCard,
                    selectedBiker?.id === biker.id && styles.selectedRiderCard
                  ]}
                  onPress={() => handleBikerSelect(biker)}
                >
                  <View style={styles.riderIcon}>
                    <Ionicons
                      name={getBikerIcon(biker.vehicleType)}
                      size={24}
                      color={colors.primary}
                    />
                  </View>
                  <View style={styles.riderInfo}>
                    <Text style={styles.riderName}>{biker.name}</Text>
                    <View style={styles.riderMeta}>
                      <View style={styles.ratingContainer}>
                        <Ionicons name="star" size={14} color={colors.warning} />
                        <Text style={styles.rating}>{biker.rating}</Text>
                      </View>
                      <Text style={styles.riderVehicleType}>
                        {biker.vehicleType === 'motorcycle' ? 'Motorcycle' : 'Bicycle'}
                      </Text>
                    </View>
                    <Text style={styles.eta}>ETA: {biker.estimatedArrival}</Text>
                  </View>
                  <View style={styles.distanceInfo}>
                    <Text style={styles.distance}>
                      {(Math.random() * 2 + 0.5).toFixed(1)} km
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Hidden MapView for testing */}
          <MapView
            style={styles.hiddenMap}
            initialRegion={{
              latitude: -17.8252,
              longitude: 31.0335,
              latitudeDelta: 0.0922,
              longitudeDelta: 0.0421,
            }}
            onMapReady={() => {
              console.log('Map is ready!');
            }}
            mapType="standard"
          >
            {/* User Location Marker */}
            <Marker
              coordinate={{
                latitude: userLocation.latitude,
                longitude: userLocation.longitude,
              }}
              title="Your Location"
              pinColor="red"
            />

            {/* Biker Markers */}
            {nearbyBikers.map((biker) => (
              <Marker
                key={biker.id}
                coordinate={{
                  latitude: biker.latitude,
                  longitude: biker.longitude,
                }}
                title={biker.name}
                description={`${biker.vehicleType} - ETA: ${biker.estimatedArrival}`}
                pinColor="blue"
                onPress={() => handleBikerSelect(biker)}
              />
            ))}
          </MapView>



          {/* Map Controls */}
          <TouchableOpacity
            style={styles.myLocationButton}
            onPress={() => {
              if (mapRef.current && userLocation) {
                mapRef.current.animateToRegion(userLocation, 1000);
              }
            }}
          >
            <Ionicons name="locate" size={24} color={colors.primary} />
          </TouchableOpacity>

          {/* Quick Request Button */}
          <TouchableOpacity
            style={styles.quickRequestButton}
            onPress={() => {
              if (nearbyBikers.length > 0) {
                handleBikerSelect(nearbyBikers[0]); // Select closest biker
              } else {
                Alert.alert('No Riders Available', 'There are no riders available in your area at the moment.');
              }
            }}
          >
            <Ionicons name="flash" size={24} color={colors.secondary} />
          </TouchableOpacity>
        </View>

        {/* Bottom Sheet */}
        <Animated.View style={[styles.bottomSheet, { height: bottomSheetHeight }]}>
          <View style={styles.bottomSheetHandle} />
          
          {!selectedBiker ? (
            <View style={styles.bottomSheetContent}>
              <Text style={styles.bottomSheetTitle}>Nearby Riders</Text>
              <Text style={styles.bottomSheetSubtitle}>
                {nearbyBikers.length} riders available in your area
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.bikersList}>
                {nearbyBikers.map((biker) => (
                  <TouchableOpacity
                    key={biker.id}
                    style={styles.bikerCard}
                    onPress={() => handleBikerSelect(biker)}
                  >
                    <View style={styles.bikerCardHeader}>
                      <Ionicons 
                        name={getBikerIcon(biker.vehicleType)} 
                        size={20} 
                        color={colors.primary} 
                      />
                      <View style={styles.ratingContainer}>
                        <Ionicons name="star" size={14} color={colors.warning} />
                        <Text style={styles.rating}>{biker.rating}</Text>
                      </View>
                    </View>
                    <Text style={styles.bikerName}>{biker.name}</Text>
                    <Text style={styles.estimatedTime}>{biker.estimatedArrival}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          ) : (
            <View style={styles.bottomSheetContent}>
              <TouchableOpacity 
                style={styles.backButton}
                onPress={() => {
                  setSelectedBiker(null);
                  collapseBottomSheet();
                }}
              >
                <Ionicons name="arrow-back" size={24} color={colors.primary} />
              </TouchableOpacity>
              
              <View style={styles.selectedBikerInfo}>
                <View style={styles.selectedBikerHeader}>
                  <View style={styles.bikerAvatar}>
                    <Ionicons 
                      name={getBikerIcon(selectedBiker.vehicleType)} 
                      size={24} 
                      color={colors.primary} 
                    />
                  </View>
                  <View style={styles.bikerDetails}>
                    <Text style={styles.selectedBikerName}>{selectedBiker.name}</Text>
                    <View style={styles.bikerMeta}>
                      <View style={styles.ratingContainer}>
                        <Ionicons name="star" size={16} color={colors.warning} />
                        <Text style={styles.selectedBikerRating}>{selectedBiker.rating}</Text>
                      </View>
                      <Text style={styles.vehicleType}>
                        {selectedBiker.vehicleType === 'motorcycle' ? 'Motorcycle' : 'Bicycle'}
                      </Text>
                    </View>
                  </View>
                </View>
                
                <View style={styles.estimatedArrivalContainer}>
                  <Ionicons name="time" size={20} color={colors.info} />
                  <Text style={styles.estimatedArrivalText}>
                    Estimated arrival: {selectedBiker.estimatedArrival}
                  </Text>
                </View>

                <TouchableOpacity 
                  style={styles.requestButton}
                  onPress={handleRequestDelivery}
                >
                  <Text style={styles.requestButtonText}>Request Delivery</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}



const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...typography.body1,
    color: colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.secondary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  userName: {
    ...typography.h3,
    color: colors.textPrimary,
    fontWeight: '600',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  notificationButton: {
    position: 'relative',
    padding: spacing.xs,
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCount: {
    ...typography.caption,
    color: colors.secondary,
    fontSize: 12,
    fontWeight: '600',
  },
  logoutButton: {
    padding: spacing.xs,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: colors.surface,
  },
  userMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.secondary,
  },
  bikerMarker: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.success,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.secondary,
  },
  selectedBikerMarker: {
    backgroundColor: colors.warning,
    transform: [{ scale: 1.2 }],
  },
  myLocationButton: {
    position: 'absolute',
    top: spacing.lg,
    right: spacing.lg,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.textPrimary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickRequestButton: {
    position: 'absolute',
    top: spacing.lg + 60, // Below the location button
    right: spacing.lg,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.textPrimary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.secondary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: colors.textPrimary,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  bottomSheetHandle: {
    width: 40,
    height: 4,
    backgroundColor: colors.border,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: spacing.sm,
  },
  bottomSheetContent: {
    flex: 1,
    padding: spacing.lg,
  },
  bottomSheetTitle: {
    ...typography.h3,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  bottomSheetSubtitle: {
    ...typography.body1,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  bikersList: {
    flexDirection: 'row',
  },
  bikerCard: {
    width: 120,
    padding: spacing.md,
    marginRight: spacing.sm,
    backgroundColor: colors.background,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  bikerCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  rating: {
    ...typography.caption,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  bikerName: {
    ...typography.body1,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  estimatedTime: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  backButton: {
    position: 'absolute',
    top: spacing.md,
    left: spacing.lg,
    zIndex: 1,
  },
  selectedBikerInfo: {
    marginTop: spacing.xl,
  },
  selectedBikerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  bikerAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
    borderWidth: 2,
    borderColor: colors.border,
  },
  bikerDetails: {
    flex: 1,
  },
  selectedBikerName: {
    ...typography.h3,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  bikerMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  selectedBikerRating: {
    ...typography.body1,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  vehicleType: {
    ...typography.body1,
    color: colors.textSecondary,
    textTransform: 'capitalize',
  },
  estimatedArrivalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.lg,
    gap: spacing.sm,
  },
  estimatedArrivalText: {
    ...typography.body1,
    color: colors.textPrimary,
    fontWeight: '500',
  },
  requestButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    borderRadius: 12,
    alignItems: 'center',
  },
  requestButtonText: {
    ...typography.body1,
    color: colors.secondary,
    fontWeight: '600',
  },
  // Debug and Alternative Map Styles
  debugOverlay: {
    position: 'absolute',
    top: 10,
    left: 10,
    right: 10,
    backgroundColor: colors.background,
    padding: spacing.sm,
    borderRadius: 8,
    zIndex: 1000,
    opacity: 0.9,
  },
  debugText: {
    ...typography.caption,
    color: colors.textPrimary,
    marginBottom: 2,
  },
  mapAlternative: {
    flex: 1,
    backgroundColor: colors.surface,
    padding: spacing.lg,
  },
  mapHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  mapTitle: {
    ...typography.h3,
    color: colors.textPrimary,
    marginLeft: spacing.md,
    fontWeight: '600',
  },
  ridersList: {
    flex: 1,
  },
  riderCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: 12,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedRiderCard: {
    borderColor: colors.primary,
    backgroundColor: colors.surface,
  },
  riderIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  riderInfo: {
    flex: 1,
  },
  riderName: {
    ...typography.body1,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  riderMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
    marginBottom: spacing.xs,
  },
  riderVehicleType: {
    ...typography.caption,
    color: colors.textSecondary,
    textTransform: 'capitalize',
  },
  eta: {
    ...typography.caption,
    color: colors.info,
    fontWeight: '500',
  },
  distanceInfo: {
    alignItems: 'center',
  },
  distance: {
    ...typography.body1,
    color: colors.primary,
    fontWeight: '600',
  },
  hiddenMap: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
  },
});
