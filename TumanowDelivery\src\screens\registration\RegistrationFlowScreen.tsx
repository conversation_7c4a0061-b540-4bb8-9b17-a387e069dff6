import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing, layout } from '../../theme';
import { useAuth } from '../../contexts/AuthContext';
import { authService } from '../../services/supabase';

// Import step components
import RoleSelectionStep from './steps/RoleSelectionStep';
import PersonalInfoStep from './steps/PersonalInfoStep';
import EmailVerificationStep from './steps/EmailVerificationStep';
import PhoneVerificationStep from './steps/PhoneVerificationStep';
import WelcomeStep from './steps/WelcomeStep';

export type UserRole = 'shop_owner' | 'client' | 'biker';

export interface RegistrationData {
  role: UserRole;
  fullName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
}

interface RegistrationFlowScreenProps {
  navigation: any;
}

const STEPS = [
  { id: 'role', title: 'Choose Role', icon: 'person' },
  { id: 'info', title: 'Personal Info', icon: 'information-circle' },
  { id: 'email', title: 'Verify Email', icon: 'mail' },
  { id: 'phone', title: 'Verify Phone', icon: 'call' },
  { id: 'welcome', title: 'Welcome!', icon: 'checkmark-circle' },
];

export default function RegistrationFlowScreen({ navigation }: RegistrationFlowScreenProps) {
  const { signUp, loading: authLoading } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [registrationData, setRegistrationData] = useState<RegistrationData>({
    role: 'client',
    fullName: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
  });

  const progressAnimation = useRef(new Animated.Value(0)).current;

  const updateRegistrationData = (data: Partial<RegistrationData>) => {
    setRegistrationData(prev => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    console.log('nextStep called - current step:', currentStep, 'step name:', STEPS[currentStep].id);
    if (currentStep < STEPS.length - 1) {
      const newStep = currentStep + 1;
      console.log('Moving to step:', newStep, 'step name:', STEPS[newStep].id);
      setCurrentStep(newStep);

      // Animate progress bar
      Animated.timing(progressAnimation, {
        toValue: (newStep / (STEPS.length - 1)) * 100,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      console.log('Already at last step, cannot proceed further');
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      
      // Animate progress bar
      Animated.timing(progressAnimation, {
        toValue: (newStep / (STEPS.length - 1)) * 100,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  };

  const handleRegistration = async () => {
    try {
      // Use the authService directly to avoid auth state changes
      const result = await authService.signUp(registrationData.email, registrationData.password, {
        fullName: registrationData.fullName,
        phoneNumber: registrationData.phoneNumber,
        role: registrationData.role,
      });

      if (result.error) {
        Alert.alert('Registration Error', (result.error as any)?.message || 'Failed to create account');
        return false;
      }

      // Check if user was created but needs email confirmation
      if (result.data?.user && !result.data.user.email_confirmed_at) {
        console.log('User created, email confirmation required');
        return true;
      }

      return true;
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Something went wrong');
      return false;
    }
  };

  const renderStep = () => {
    console.log('Rendering step:', currentStep, 'step name:', STEPS[currentStep].id);
    const stepProps = {
      data: registrationData,
      updateData: updateRegistrationData,
      onNext: nextStep,
      onPrev: prevStep,
      onRegister: handleRegistration,
      loading: authLoading,
    };

    switch (STEPS[currentStep].id) {
      case 'role':
        return <RoleSelectionStep {...stepProps} />;
      case 'info':
        return <PersonalInfoStep {...stepProps} />;
      case 'email':
        return <EmailVerificationStep {...stepProps} />;
      case 'phone':
        return <PhoneVerificationStep {...stepProps} />;
      case 'welcome':
        return <WelcomeStep {...stepProps} navigation={navigation} />;
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with Progress */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          {currentStep > 0 && (
            <TouchableOpacity onPress={prevStep} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
          )}
          <Text style={styles.stepCounter}>
            {currentStep + 1} of {STEPS.length}
          </Text>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBackground}>
            <Animated.View 
              style={[
                styles.progressFill,
                {
                  width: progressAnimation.interpolate({
                    inputRange: [0, 100],
                    outputRange: ['0%', '100%'],
                    extrapolate: 'clamp',
                  }),
                },
              ]}
            />
          </View>
        </View>

        {/* Step Title */}
        <Text style={styles.stepTitle}>{STEPS[currentStep].title}</Text>
      </View>

      {/* Step Content */}
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {renderStep()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  backButton: {
    padding: spacing.xs,
  },
  stepCounter: {
    ...typography.subtitle2,
    color: colors.textSecondary,
  },
  closeButton: {
    padding: spacing.xs,
  },
  progressContainer: {
    marginBottom: spacing.md,
  },
  progressBackground: {
    height: 4,
    backgroundColor: colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  stepTitle: {
    ...typography.h4,
    color: colors.text,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
});
