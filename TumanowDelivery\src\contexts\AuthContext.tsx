import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { authService, UserProfile, supabase } from '../services/supabase';

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, userData: {
    fullName: string;
    phoneNumber: string;
    role: 'shop_owner' | 'client' | 'biker';
  }) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  completeRegistration: () => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          setUser(session.user);
          await loadUserProfile(session.user.id);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        if (session?.user) {
          // Always set the user if they have a session
          setUser(session.user);

          if (session.user.email_confirmed_at) {
            const profile = await loadUserProfile(session.user.id);

            // If user has a profile but registration is not completed, keep them in registration flow
            if (profile && !profile.registration_completed) {
              console.log('User email confirmed but registration not completed, staying in registration flow');
              // Don't set user to null - this breaks the registration flow
              // The App.tsx will handle showing registration vs dashboard based on profile.registration_completed
            }
          } else {
            // User exists but email not confirmed - keep them in registration flow
            console.log('User email not confirmed, staying in registration flow');
          }
        } else {
          setUser(null);
          setUserProfile(null);
        }

        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const loadUserProfile = async (userId: string) => {
    try {
      const { data, error } = await authService.getUserProfile(userId);
      if (error) {
        console.error('Error loading user profile:', error);

        // If profile doesn't exist, check if user has metadata and create profile
        if ((error as any)?.code === 'PGRST116') {
          const { data: { user } } = await supabase.auth.getUser();
          if (user && user.user_metadata) {
            const metadata = user.user_metadata;
            if (metadata.full_name && metadata.phone_number && metadata.role) {
              console.log('Creating missing user profile from metadata...');
              const createResult = await authService.createUserProfile(userId, user.email!, {
                fullName: metadata.full_name,
                phoneNumber: metadata.phone_number,
                role: metadata.role,
              });

              if (!createResult.error) {
                // Retry loading the profile
                const { data: newData, error: newError } = await authService.getUserProfile(userId);
                if (!newError) {
                  setUserProfile(newData);
                  return newData;
                }
              } else {
                // If profile creation failed due to duplicate, try to load existing profile
                console.log('Profile creation failed, attempting to load existing profile...');
                const { data: existingData, error: existingError } = await authService.getUserProfile(userId);
                if (!existingError && existingData) {
                  setUserProfile(existingData);
                  return existingData;
                }
              }
            }
          }
        }
        return null;
      }
      setUserProfile(data);
      return data;
    } catch (error) {
      console.error('Error loading user profile:', error);
      return null;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await authService.signIn(email, password);
      
      if (error) {
        return { error };
      }

      // User state will be updated by the auth state change listener
      return { error: null };
    } catch (error) {
      return { error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (
    email: string, 
    password: string, 
    userData: {
      fullName: string;
      phoneNumber: string;
      role: 'shop_owner' | 'client' | 'biker';
    }
  ) => {
    try {
      setLoading(true);
      const { data, error } = await authService.signUp(email, password, userData);
      
      if (error) {
        return { error };
      }

      // User state will be updated by the auth state change listener
      return { error: null };
    } catch (error) {
      return { error };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      await authService.signOut();
      // User state will be updated by the auth state change listener
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshProfile = async () => {
    if (user) {
      await loadUserProfile(user.id);
    }
  };

  const completeRegistration = async () => {
    try {
      if (!user) {
        return { error: 'No authenticated user' };
      }

      const { error } = await authService.completeRegistration(user.id);
      if (error) {
        return { error };
      }

      // Refresh the profile to get the updated registration_completed status
      await refreshProfile();

      return { error: null };
    } catch (error) {
      return { error };
    }
  };

  const value: AuthContextType = {
    user,
    userProfile,
    loading,
    signIn,
    signUp,
    signOut,
    refreshProfile,
    completeRegistration,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
