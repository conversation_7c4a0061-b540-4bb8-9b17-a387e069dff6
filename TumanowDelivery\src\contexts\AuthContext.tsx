import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { authService, UserProfile, supabase } from '../services/supabase';

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, userData: {
    fullName: string;
    phoneNumber: string;
    role: 'shop_owner' | 'client' | 'biker';
  }) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          setUser(session.user);
          await loadUserProfile(session.user.id);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        if (session?.user) {
          // Only set user as signed in if their email is confirmed
          // This prevents unconfirmed users from being redirected to dashboards
          if (session.user.email_confirmed_at) {
            setUser(session.user);
            await loadUserProfile(session.user.id);
          } else {
            // User exists but email not confirmed - keep them in registration flow
            console.log('User email not confirmed, staying in registration flow');
            setUser(null);
            setUserProfile(null);
          }
        } else {
          setUser(null);
          setUserProfile(null);
        }

        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const loadUserProfile = async (userId: string) => {
    try {
      const { data, error } = await authService.getUserProfile(userId);
      if (error) {
        console.error('Error loading user profile:', error);

        // If profile doesn't exist, check if user has metadata and create profile
        if ((error as any)?.code === 'PGRST116') {
          const { data: { user } } = await supabase.auth.getUser();
          if (user && user.user_metadata) {
            const metadata = user.user_metadata;
            if (metadata.full_name && metadata.phone_number && metadata.role) {
              console.log('Creating missing user profile from metadata...');
              const createResult = await authService.createUserProfile(userId, user.email!, {
                fullName: metadata.full_name,
                phoneNumber: metadata.phone_number,
                role: metadata.role,
              });

              if (!createResult.error) {
                // Retry loading the profile
                const { data: newData, error: newError } = await authService.getUserProfile(userId);
                if (!newError) {
                  setUserProfile(newData);
                  return;
                }
              }
            }
          }
        }
        return;
      }
      setUserProfile(data);
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await authService.signIn(email, password);
      
      if (error) {
        return { error };
      }

      // User state will be updated by the auth state change listener
      return { error: null };
    } catch (error) {
      return { error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (
    email: string, 
    password: string, 
    userData: {
      fullName: string;
      phoneNumber: string;
      role: 'shop_owner' | 'client' | 'biker';
    }
  ) => {
    try {
      setLoading(true);
      const { data, error } = await authService.signUp(email, password, userData);
      
      if (error) {
        return { error };
      }

      // User state will be updated by the auth state change listener
      return { error: null };
    } catch (error) {
      return { error };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      await authService.signOut();
      // User state will be updated by the auth state change listener
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshProfile = async () => {
    if (user) {
      await loadUserProfile(user.id);
    }
  };

  const value: AuthContextType = {
    user,
    userProfile,
    loading,
    signIn,
    signUp,
    signOut,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
