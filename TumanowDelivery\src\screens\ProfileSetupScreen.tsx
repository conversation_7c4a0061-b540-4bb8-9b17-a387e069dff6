import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Button, Input } from '../components/common';
import { colors, typography, spacing, layout, commonStyles } from '../theme';
import { useAuth } from '../contexts/AuthContext';
import { authService } from '../services/supabase';

type UserRole = 'shop_owner' | 'client' | 'biker';

export default function ProfileSetupScreen() {
  const { refreshProfile } = useAuth();
  const [selectedRole, setSelectedRole] = useState<UserRole>('client');
  const [formData, setFormData] = useState({
    fullName: '',
    phoneNumber: '',
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const roles = [
    {
      id: 'client' as UserRole,
      title: 'Customer',
      description: 'Order deliveries from shops',
      icon: 'person',
      color: colors.client,
    },
    {
      id: 'shop_owner' as UserRole,
      title: 'Shop Owner',
      description: 'Send parcels to customers',
      icon: 'storefront',
      color: colors.shopOwner,
    },
    {
      id: 'biker' as UserRole,
      title: 'Delivery Rider',
      description: 'Deliver parcels and earn money',
      icon: 'bicycle',
      color: colors.biker,
    },
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const result = await authService.createProfileForExistingUser({
        fullName: formData.fullName.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        role: selectedRole,
      });

      if (result.error) {
        Alert.alert('Error', result.error.message || 'Failed to create profile. Please try again.');
        return;
      }

      // Refresh the profile to trigger navigation
      await refreshProfile();
      Alert.alert('Success', 'Profile created successfully!');
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Complete Your Profile</Text>
          <Text style={styles.subtitle}>
            We need a few more details to set up your account
          </Text>
        </View>

        {/* Role Selection */}
        <View style={styles.roleSection}>
          <Text style={styles.sectionTitle}>I am a:</Text>
          <View style={styles.rolesContainer}>
            {roles.map((role) => (
              <TouchableOpacity
                key={role.id}
                style={[
                  styles.roleCard,
                  selectedRole === role.id && styles.roleCardSelected,
                ]}
                onPress={() => setSelectedRole(role.id)}
              >
                <Ionicons
                  name={role.icon as any}
                  size={32}
                  color={selectedRole === role.id ? colors.secondary : role.color}
                />
                <Text style={[
                  styles.roleTitle,
                  selectedRole === role.id && styles.roleTextSelected,
                ]}>
                  {role.title}
                </Text>
                <Text style={[
                  styles.roleDescription,
                  selectedRole === role.id && styles.roleTextSelected,
                ]}>
                  {role.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Form */}
        <View style={styles.formSection}>
          <Input
            label="Full Name"
            placeholder="Enter your full name"
            value={formData.fullName}
            onChangeText={(value) => updateFormData('fullName', value)}
            error={errors.fullName}
            leftIcon="person"
          />

          <Input
            label="Phone Number"
            placeholder="Enter your phone number"
            value={formData.phoneNumber}
            onChangeText={(value) => updateFormData('phoneNumber', value)}
            keyboardType="phone-pad"
            error={errors.phoneNumber}
            leftIcon="call"
          />
        </View>

        {/* Submit Button */}
        <View style={styles.buttonSection}>
          <Button
            title="Complete Setup"
            onPress={handleSubmit}
            loading={loading}
            variant="gradient"
            size="large"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  header: {
    paddingVertical: spacing.xl,
    alignItems: 'center',
  },
  title: {
    ...typography.h2,
    color: colors.text,
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body1,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  roleSection: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    ...typography.h5,
    color: colors.text,
    marginBottom: spacing.md,
  },
  rolesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  roleCard: {
    flex: 1,
    backgroundColor: colors.surface,
    borderRadius: layout.cardRadius,
    padding: spacing.md,
    marginHorizontal: spacing.xs,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.border,
  },
  roleCardSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  roleTitle: {
    ...typography.subtitle2,
    color: colors.text,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  roleDescription: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  roleTextSelected: {
    color: colors.secondary,
  },
  formSection: {
    marginBottom: spacing.xl,
  },
  buttonSection: {
    marginBottom: spacing.xl,
  },
});
