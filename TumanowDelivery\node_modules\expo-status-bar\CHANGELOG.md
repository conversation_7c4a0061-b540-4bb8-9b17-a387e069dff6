# Changelog

## Unpublished

### 🛠 Breaking changes

### 🎉 New features

### 🐛 Bug fixes

### 💡 Others

## 2.2.3 — 2025-04-30

_This version does not introduce any user-facing changes._

## 2.2.2 — 2025-04-25

_This version does not introduce any user-facing changes._

## 2.2.1 — 2025-04-14

### 🎉 New features

- Support imperative functions from `SystemBars` when edge-to-edge is enabled. ([#36156](https://github.com/expo/expo/pull/36156) by [@behenate](https://github.com/behenate))

## 2.2.0 — 2025-04-11

### 🎉 New features

- Use react-native-edge-to-edge system bars when edge-to-edge is enabled ([#36087](https://github.com/expo/expo/pull/36087) by [@brentvatne](https://github.com/brentvatne))

## 2.1.0 — 2025-04-04

- Warn about potential edge-to-edge interferences. ([#34478](https://github.com/expo/expo/pull/34478) by [@zoontek](https://github.com/zoontek))

## 2.0.1 - 2025-01-10

_This version does not introduce any user-facing changes._

## 2.0.0 — 2024-10-22

### 🐛 Bug fixes

- Add missing `react`/`react-native` peer dependencies. ([#30573](https://github.com/expo/expo/pull/30573) by [@byCedric](https://github.com/byCedric))

### 💡 Others

- Minimize modules. ([#31088](https://github.com/expo/expo/pull/31088) by [@EvanBacon](https://github.com/EvanBacon))

## 1.12.1 — 2024-04-23

_This version does not introduce any user-facing changes._

## 1.12.0 — 2024-04-18

_This version does not introduce any user-facing changes._

## 1.11.1 - 2023-12-19

_This version does not introduce any user-facing changes._

## 1.11.0 — 2023-12-12

_This version does not introduce any user-facing changes._

## 1.10.0 — 2023-11-14

### 🐛 Bug fixes

- Made the `setStatusBarHidden` methods `animation` parameter optional to match the documentation. ([#23866](https://github.com/expo/expo/pull/23866) by [@DoctorJohn](https://github.com/DoctorJohn))

## 1.9.0 — 2023-10-17

### 💡 Others

- Ship untranspiled JSX to support custom handling of `jsx` and `createElement`. ([#24889](https://github.com/expo/expo/pull/24889) by [@EvanBacon](https://github.com/EvanBacon))

## 1.8.0 — 2023-09-15

### 💡 Others

- Reduce web bundle size. ([#24297](https://github.com/expo/expo/pull/24297) by [@EvanBacon](https://github.com/EvanBacon))

## 1.7.1 — 2023-08-02

_This version does not introduce any user-facing changes._

## 1.7.0 — 2023-07-28

### 🎉 New features

- Added support for `animated` property in `setStatusBarStyle`. ([#23408](https://github.com/expo/expo/pull/23408) by [@haikov](https://github.com/haikov))

## 1.6.0 — 2023-06-21

_This version does not introduce any user-facing changes._

## 1.5.0 — 2023-05-08

_This version does not introduce any user-facing changes._

## 1.4.4 — 2023-02-09

_This version does not introduce any user-facing changes._

## 1.4.3 — 2023-02-03

### 💡 Others

- On Android bump `compileSdkVersion` and `targetSdkVersion` to `33`. ([#20721](https://github.com/expo/expo/pull/20721) by [@lukmccall](https://github.com/lukmccall))

## 1.4.2 — 2022-11-02

_This version does not introduce any user-facing changes._

## 1.4.1 — 2022-10-25

_This version does not introduce any user-facing changes._

## 1.4.0 — 2022-07-07

_This version does not introduce any user-facing changes._

## 1.3.0 — 2022-04-18

_This version does not introduce any user-facing changes._

## 1.2.0 — 2021-12-03

_This version does not introduce any user-facing changes._

## 1.1.0 — 2021-09-09

_This version does not introduce any user-facing changes._

## 1.0.4 — 2021-03-10

_This version does not introduce any user-facing changes._

## 1.0.3 — 2020-11-17

_This version does not introduce any user-facing changes._

## 1.0.2 — 2020-06-25

### 🐛 Bug fixes

- Provide web fallback for styleToBarStyle in order to not produce a warning.
