import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, typography, spacing } from '../../../theme';
import { Button } from '../../../components/common';
import { RegistrationData } from '../RegistrationFlowScreen';

interface WelcomeStepProps {
  data: RegistrationData;
  navigation: any;
}

const { width } = Dimensions.get('window');

const roleMessages = {
  client: {
    title: 'Welcome to Tumanow!',
    subtitle: 'Your delivery journey starts now',
    message: 'Get ready to experience fast, reliable deliveries right to your doorstep.',
    features: [
      'Track your orders in real-time',
      'Multiple payment options',
      'Rate and review your experience',
      '24/7 customer support',
    ],
    cta: 'Start Ordering',
    icon: 'bag-check',
  },
  shop_owner: {
    title: 'Welcome, Business Owner!',
    subtitle: 'Grow your business with <PERSON><PERSON>ow',
    message: 'Reach more customers and increase your sales with our delivery platform.',
    features: [
      'Manage orders efficiently',
      'Track delivery progress',
      'Analytics and insights',
      'Dedicated business support',
    ],
    cta: 'Set Up Shop',
    icon: 'storefront',
  },
  biker: {
    title: 'Welcome, Delivery Hero!',
    subtitle: 'Start earning on your schedule',
    message: 'Join our community of riders and make money while helping your community.',
    features: [
      'Flexible working hours',
      'Competitive earnings',
      'Real-time navigation',
      'Rider support community',
    ],
    cta: 'Start Delivering',
    icon: 'bicycle',
  },
};

export default function WelcomeStep({ data, navigation }: WelcomeStepProps) {
  const scaleAnimation = useRef(new Animated.Value(0)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;

  const roleData = roleMessages[data.role];

  useEffect(() => {
    // Start animations
    Animated.sequence([
      Animated.timing(scaleAnimation, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnimation, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const handleGetStarted = () => {
    // The user is already signed in and has a profile
    // The App.tsx navigation will automatically redirect to the appropriate dashboard
    // We don't need to do any navigation here - just let the auth state handle it
    console.log('Registration completed! Auth state will handle navigation to dashboard.');
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[colors.background, colors.surface]}
        style={styles.gradient}
      >
        {/* Success Animation */}
        <View style={styles.content}>
          <Animated.View 
            style={[
              styles.iconContainer,
              { transform: [{ scale: scaleAnimation }] }
            ]}
          >
            <View style={styles.successCircle}>
              <Ionicons name="checkmark" size={48} color={colors.secondary} />
            </View>
          </Animated.View>

          <Animated.View 
            style={[
              styles.textContainer,
              { 
                opacity: fadeAnimation,
                transform: [{ translateY: slideAnimation }]
              }
            ]}
          >
            {/* Welcome Message */}
            <Text style={styles.title}>{roleData.title}</Text>
            <Text style={styles.subtitle}>{roleData.subtitle}</Text>
            <Text style={styles.message}>{roleData.message}</Text>

            {/* User Info */}
            <View style={styles.userInfo}>
              <View style={styles.userAvatar}>
                <Text style={styles.userInitial}>
                  {data.fullName.charAt(0).toUpperCase()}
                </Text>
              </View>
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{data.fullName}</Text>
                <Text style={styles.userEmail}>{data.email}</Text>
                <View style={styles.roleTag}>
                  <Ionicons name={roleData.icon as any} size={16} color={colors.primary} />
                  <Text style={styles.roleText}>
                    {roleData.title.split(' ')[1] || roleData.title.split(',')[0]}
                  </Text>
                </View>
              </View>
            </View>

            {/* Features */}
            <View style={styles.featuresContainer}>
              <Text style={styles.featuresTitle}>What's next?</Text>
              {roleData.features.map((feature, index) => (
                <Animated.View
                  key={index}
                  style={[
                    styles.featureItem,
                    {
                      opacity: fadeAnimation,
                      transform: [{
                        translateX: slideAnimation.interpolate({
                          inputRange: [0, 50],
                          outputRange: [0, 100],
                        })
                      }]
                    }
                  ]}
                >
                  <View style={styles.featureIcon}>
                    <Ionicons name="checkmark-circle" size={20} color={colors.success} />
                  </View>
                  <Text style={styles.featureText}>{feature}</Text>
                </Animated.View>
              ))}
            </View>
          </Animated.View>
        </View>

        {/* Footer */}
        <Animated.View 
          style={[
            styles.footer,
            { 
              opacity: fadeAnimation,
              transform: [{ translateY: slideAnimation }]
            }
          ]}
        >
          <Button
            title={roleData.cta}
            onPress={handleGetStarted}
            variant="gradient"
            size="large"
            rightIcon="arrow-forward"
          />
          
          <Text style={styles.footerText}>
            You can always update your preferences in settings
          </Text>
        </Animated.View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: spacing.xl,
  },
  successCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.success,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.success,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  textContainer: {
    alignItems: 'center',
    width: '100%',
  },
  title: {
    ...typography.h2,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.h5,
    color: colors.primary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  message: {
    ...typography.body1,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.xl,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.lg,
    borderRadius: 16,
    marginBottom: spacing.xl,
    width: '100%',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  userInitial: {
    ...typography.h4,
    color: colors.secondary,
    fontWeight: 'bold',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...typography.h5,
    color: colors.text,
    marginBottom: spacing.xs,
  },
  userEmail: {
    ...typography.body2,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  roleTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    alignSelf: 'flex-start',
    gap: spacing.xs,
  },
  roleText: {
    ...typography.caption,
    color: colors.primary,
    fontWeight: '600',
  },
  featuresContainer: {
    width: '100%',
    marginBottom: spacing.xl,
  },
  featuresTitle: {
    ...typography.h5,
    color: colors.text,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md,
  },
  featureIcon: {
    marginRight: spacing.md,
  },
  featureText: {
    ...typography.body1,
    color: colors.text,
    flex: 1,
  },
  footer: {
    paddingVertical: spacing.xl,
  },
  footerText: {
    ...typography.caption,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.md,
  },
});
