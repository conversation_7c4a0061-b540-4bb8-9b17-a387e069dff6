import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing } from '../../../theme';
import { Button, Input } from '../../../components/common';
import { RegistrationData } from '../RegistrationFlowScreen';
import { supabase } from '../../../services/supabase';

interface PersonalInfoStepProps {
  data: RegistrationData;
  updateData: (data: Partial<RegistrationData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export default function PersonalInfoStep({ data, updateData, onNext, onPrev }: PersonalInfoStepProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Full name validation
    if (!data.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    } else if (data.fullName.trim().length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters';
    }

    // Email validation
    if (!data.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email.trim())) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    if (!data.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!/^[\+]?[0-9\s\-\(\)]{10,}$/.test(data.phoneNumber.trim())) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    // Password validation
    if (!data.password) {
      newErrors.password = 'Password is required';
    } else if (data.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(data.password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and number';
    }

    // Confirm password validation
    if (!data.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (data.password !== data.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    if (!validateForm()) {
      return;
    }

    // Check if email is already registered
    try {
      const { data: existingUsers, error } = await supabase
        .from('profiles')
        .select('id, email')
        .eq('email', data.email.trim())
        .limit(1);

      if (error) {
        console.error('Error checking existing email:', error);
        Alert.alert('Error', 'Failed to validate email. Please try again.');
        return;
      }

      if (existingUsers && existingUsers.length > 0) {
        Alert.alert(
          'Email Already Registered',
          'This email address is already registered. Please use a different email or try logging in instead.',
          [
            { text: 'OK', style: 'default' }
          ]
        );
        return;
      }

      // Email is available, proceed to next step
      onNext();
    } catch (error) {
      console.error('Error validating email:', error);
      Alert.alert('Error', 'Failed to validate email. Please try again.');
    }
  };

  const updateField = (field: keyof RegistrationData, value: string) => {
    updateData({ [field]: value });
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^a-zA-Z\d]/.test(password)) strength++;
    return strength;
  };

  const passwordStrength = getPasswordStrength(data.password);
  const strengthColors = ['#DC3545', '#FFC107', '#FFC107', '#28A745', '#28A745'];
  const strengthLabels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Ionicons name="person-add" size={32} color={colors.primary} />
          </View>
          <Text style={styles.title}>Tell us about yourself</Text>
          <Text style={styles.subtitle}>
            We need some basic information to create your account
          </Text>
        </View>

        {/* Form */}
        <View style={styles.form}>
          <Input
            label="Full Name"
            placeholder="Enter your full name"
            value={data.fullName}
            onChangeText={(value) => updateField('fullName', value)}
            error={errors.fullName}
            leftIcon="person"
            autoCapitalize="words"
          />

          <Input
            label="Email Address"
            placeholder="Enter your email"
            value={data.email}
            onChangeText={(value) => updateField('email', value.toLowerCase())}
            error={errors.email}
            leftIcon="mail"
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <Input
            label="Phone Number"
            placeholder="Enter your phone number"
            value={data.phoneNumber}
            onChangeText={(value) => updateField('phoneNumber', value)}
            error={errors.phoneNumber}
            leftIcon="call"
            keyboardType="phone-pad"
          />

          <Input
            label="Password"
            placeholder="Create a strong password"
            value={data.password}
            onChangeText={(value) => updateField('password', value)}
            error={errors.password}
            leftIcon="lock-closed"
            secureTextEntry={!showPassword}
            rightIcon={showPassword ? "eye-off" : "eye"}
            onRightIconPress={() => setShowPassword(!showPassword)}
          />

          {/* Password Strength Indicator */}
          {data.password.length > 0 && (
            <View style={styles.passwordStrength}>
              <View style={styles.strengthBars}>
                {[1, 2, 3, 4, 5].map((level) => (
                  <View
                    key={level}
                    style={[
                      styles.strengthBar,
                      {
                        backgroundColor: level <= passwordStrength 
                          ? strengthColors[passwordStrength - 1] 
                          : colors.border,
                      },
                    ]}
                  />
                ))}
              </View>
              <Text style={[
                styles.strengthText,
                { color: strengthColors[passwordStrength - 1] || colors.textSecondary }
              ]}>
                {strengthLabels[passwordStrength - 1] || 'Too Short'}
              </Text>
            </View>
          )}

          <Input
            label="Confirm Password"
            placeholder="Confirm your password"
            value={data.confirmPassword}
            onChangeText={(value) => updateField('confirmPassword', value)}
            error={errors.confirmPassword}
            leftIcon="lock-closed"
            secureTextEntry={!showConfirmPassword}
            rightIcon={showConfirmPassword ? "eye-off" : "eye"}
            onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
          />
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.buttonRow}>
          <Button
            title="Back"
            onPress={onPrev}
            variant="outline"
            size="large"
            style={styles.backButton}
          />
          <Button
            title="Continue"
            onPress={handleContinue}
            variant="gradient"
            size="large"
            style={styles.continueButton}
            rightIcon="arrow-forward"
          />
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  title: {
    ...typography.h3,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body1,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
    gap: spacing.md,
  },
  passwordStrength: {
    marginTop: -spacing.sm,
    marginBottom: spacing.sm,
  },
  strengthBars: {
    flexDirection: 'row',
    gap: spacing.xs,
    marginBottom: spacing.xs,
  },
  strengthBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
  },
  strengthText: {
    ...typography.caption,
    textAlign: 'center',
  },
  footer: {
    paddingVertical: spacing.lg,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  backButton: {
    flex: 1,
  },
  continueButton: {
    flex: 2,
  },
});
