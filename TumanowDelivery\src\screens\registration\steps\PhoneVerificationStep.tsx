import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing } from '../../../theme';
import { Button } from '../../../components/common';
import { RegistrationData } from '../RegistrationFlowScreen';
import { supabase } from '../../../services/supabase';

interface PhoneVerificationStepProps {
  data: RegistrationData;
  updateData: (data: Partial<RegistrationData>) => void;
  onNext: () => void;
  onPrev: () => void;
}

export default function PhoneVerificationStep({ 
  data, 
  onNext, 
  onPrev 
}: PhoneVerificationStepProps) {
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [timeLeft, setTimeLeft] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  
  const inputRefs = useRef<(TextInput | null)[]>([]);
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // For testing purposes, skip SMS sending and go directly to code input
    console.log('Skipping SMS sending for testing, showing code input directly');
    setCodeSent(true);
    Alert.alert(
      'Testing Mode',
      'SMS sending skipped for testing. Use code: 123456',
      [{ text: 'OK' }]
    );
  }, []);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  useEffect(() => {
    // Pulse animation for the phone icon
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulse.start();
    return () => pulse.stop();
  }, []);

  const sendSMSCode = async () => {
    try {
      console.log('Sending SMS code to:', data.phoneNumber);

      // Format phone number for Supabase (E.164 format with + prefix)
      const formattedPhone = data.phoneNumber.startsWith('+')
        ? data.phoneNumber
        : `+996${data.phoneNumber.replace(/^0/, '')}`;

      console.log('Formatted phone number:', formattedPhone);

      // For existing users, we need to send OTP for phone verification
      // Use the phone change OTP method for existing users
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        // Send OTP for phone number verification/update
        const { error } = await supabase.auth.updateUser({
          phone: formattedPhone
        });

        if (error) {
          console.error('SMS sending error:', error);
          // Still set codeSent to true to allow testing with manual code
          setCodeSent(true);
          Alert.alert(
            'SMS Error',
            `SMS sending failed, but you can still test with code: 123456\n\nError: ${error.message}`,
            [{ text: 'OK' }]
          );
          return;
        }
      } else {
        console.error('No user session found');
        // Still set codeSent to true to allow testing
        setCodeSent(true);
        Alert.alert(
          'Session Error',
          'User session not found, but you can still test with code: 123456',
          [{ text: 'OK' }]
        );
        return;
      }

      // Success - set state and show success message
      setCodeSent(true);
      console.log('SMS code sent successfully');
      Alert.alert(
        'Code Sent',
        `A verification code has been sent to ${data.phoneNumber}. For testing, use code: 123456`,
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      console.error('SMS sending error:', error);
      // Always set codeSent to true to allow testing
      setCodeSent(true);
      Alert.alert(
        'Error',
        `Failed to send verification code, but you can still test with code: 123456\n\nError: ${error.message}`,
        [{ text: 'OK' }]
      );
    }
  };

  const handleCodeChange = (value: string, index: number) => {
    if (value.length > 1) return; // Prevent multiple characters
    
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-verify when all digits are entered
    if (newCode.every(digit => digit !== '') && newCode.join('').length === 6) {
      handleVerifyCode(newCode.join(''));
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyCode = async (verificationCode: string) => {
    if (verificationCode.length !== 6) {
      Alert.alert('Invalid Code', 'Please enter the complete 6-digit code');
      return;
    }

    setVerifying(true);
    try {
      console.log('Verifying SMS code:', verificationCode);

      // For testing, accept the test code directly
      if (verificationCode === '123456') {
        console.log('Test code accepted, proceeding to next step');
        onNext();
        return;
      }

      // Format phone number for Supabase (E.164 format with + prefix)
      const formattedPhone = data.phoneNumber.startsWith('+')
        ? data.phoneNumber
        : `+996${data.phoneNumber.replace(/^0/, '')}`;

      const { data: verifyData, error } = await supabase.auth.verifyOtp({
        phone: formattedPhone,
        token: verificationCode,
        type: 'phone_change'
      });

      if (error) {
        console.error('SMS verification error:', error);
        Alert.alert('Verification Failed', error.message || 'Invalid verification code. For testing, use: 123456');
        // Clear the code
        setCode(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
        return;
      }

      console.log('SMS verification successful:', verifyData);
      onNext();
    } catch (error: any) {
      console.error('SMS verification error:', error);
      Alert.alert('Error', error.message || 'For testing, use code: 123456');
    } finally {
      setVerifying(false);
    }
  };

  const handleResendCode = async () => {
    try {
      await sendSMSCode();
      setTimeLeft(60);
      setCanResend(false);
      setCode(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Something went wrong');
    }
  };

  const handleSkip = () => {
    Alert.alert(
      'Skip Phone Verification?',
      'You can verify your phone number later in your profile settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Skip', onPress: onNext },
      ]
    );
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatPhoneNumber = (phone: string) => {
    // Simple formatting for display
    if (phone.length >= 10) {
      return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    }
    return phone;
  };

  if (!codeSent) {
    return (
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <Ionicons name="hourglass" size={32} color={colors.primary} />
            </View>
            <Text style={styles.title}>Sending verification code...</Text>
            <Text style={styles.subtitle}>
              Please wait while we send a code to your phone
            </Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Animated.View 
            style={[
              styles.iconContainer,
              { transform: [{ scale: pulseAnimation }] }
            ]}
          >
            <Ionicons name="call" size={32} color={colors.primary} />
          </Animated.View>
          <Text style={styles.title}>Verify your phone</Text>
          <Text style={styles.subtitle}>
            We've sent a 6-digit code to
          </Text>
          <Text style={styles.phone}>{formatPhoneNumber(data.phoneNumber)}</Text>
        </View>

        {/* Code Input */}
        <View style={styles.codeContainer}>
          <Text style={styles.codeLabel}>Enter verification code</Text>
          <View style={styles.codeInputs}>
            {code.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref) => { inputRefs.current[index] = ref; }}
                style={[
                  styles.codeInput,
                  digit && styles.codeInputFilled,
                ]}
                value={digit}
                onChangeText={(value) => handleCodeChange(value, index)}
                onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                keyboardType="numeric"
                maxLength={1}
                selectTextOnFocus
                autoFocus={index === 0}
              />
            ))}
          </View>
        </View>

        {/* Resend Code */}
        <View style={styles.resendContainer}>
          {canResend ? (
            <TouchableOpacity onPress={handleResendCode} style={styles.resendButton}>
              <Text style={styles.resendText}>Resend Code</Text>
            </TouchableOpacity>
          ) : (
            <Text style={styles.timerText}>
              Resend code in {formatTime(timeLeft)}
            </Text>
          )}
        </View>

        {/* Skip Option */}
        <View style={styles.skipContainer}>
          <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
            <Text style={styles.skipText}>Skip for now</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.buttonRow}>
          <Button
            title="Back"
            onPress={onPrev}
            variant="outline"
            size="large"
            style={styles.backButton}
          />
          <Button
            title="Verify"
            onPress={() => handleVerifyCode(code.join(''))}
            variant="gradient"
            size="large"
            style={styles.verifyButton}
            loading={verifying}
            disabled={code.some(digit => !digit)}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  title: {
    ...typography.h3,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body1,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  phone: {
    ...typography.subtitle1,
    color: colors.primary,
    textAlign: 'center',
  },
  codeContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  codeLabel: {
    ...typography.subtitle2,
    color: colors.text,
    marginBottom: spacing.md,
  },
  codeInputs: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  codeInput: {
    width: 48,
    height: 56,
    borderWidth: 2,
    borderColor: colors.border,
    borderRadius: 12,
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    backgroundColor: colors.surface,
  },
  codeInputFilled: {
    borderColor: colors.primary,
    backgroundColor: colors.background,
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  resendButton: {
    padding: spacing.sm,
  },
  resendText: {
    ...typography.subtitle2,
    color: colors.primary,
  },
  timerText: {
    ...typography.body2,
    color: colors.textSecondary,
  },
  skipContainer: {
    alignItems: 'center',
  },
  skipButton: {
    padding: spacing.sm,
  },
  skipText: {
    ...typography.body2,
    color: colors.textSecondary,
    textDecorationLine: 'underline',
  },
  footer: {
    paddingVertical: spacing.lg,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  backButton: {
    flex: 1,
  },
  verifyButton: {
    flex: 2,
  },
});
