import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import 'react-native-url-polyfill/auto';

// Supabase configuration
const supabaseUrl = 'https://ruivinhiknpymsvdeyyw.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ1aXZpbmhpa25weW1zdmRleXl3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMwMTU5MjMsImV4cCI6MjA2ODU5MTkyM30.JSAyKoXN4u-lQsz-WEaZAWk1eIQgl3RshIsIcnBPKpI';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Types for our database
export interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  phone_number: string;
  role: 'shop_owner' | 'client' | 'biker';
  registration_completed?: boolean;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Shop {
  id: string;
  owner_id: string;
  name: string;
  description?: string;
  address: string;
  phone_number: string;
  category: string;
  latitude?: number;
  longitude?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  shop_id: string;
  client_id: string;
  biker_id?: string;
  items: OrderItem[];
  pickup_address: string;
  delivery_address: string;
  pickup_latitude?: number;
  pickup_longitude?: number;
  delivery_latitude?: number;
  delivery_longitude?: number;
  status: 'pending' | 'accepted' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled';
  total_amount: number;
  delivery_fee: number;
  notes?: string;
  estimated_delivery_time?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  name: string;
  quantity: number;
  price?: number;
  description?: string;
}

export interface BikerProfile {
  id: string;
  user_id: string;
  vehicle_type: 'bicycle' | 'motorcycle' | 'car';
  license_number?: string;
  is_verified: boolean;
  is_online: boolean;
  current_latitude?: number;
  current_longitude?: number;
  rating: number;
  total_deliveries: number;
  created_at: string;
  updated_at: string;
}

// Authentication functions
export const authService = {
  // Sign up with email and password
  async signUp(email: string, password: string, userData: {
    fullName: string;
    phoneNumber: string;
    role: 'shop_owner' | 'client' | 'biker';
  }) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: userData.fullName,
            phone_number: userData.phoneNumber,
            role: userData.role,
          },
          emailRedirectTo: undefined, // Disable email link, we'll use OTP
        },
      });

      if (error) throw error;

      // Don't create profile yet - wait for email verification
      // Profile will be created after OTP verification

      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Create user profile (separate method for reuse)
  async createUserProfile(userId: string, email: string, userData: {
    fullName: string;
    phoneNumber: string;
    role: 'shop_owner' | 'client' | 'biker';
  }) {
    try {
      console.log('Creating user profile with data:', {
        id: userId,
        email: email,
        full_name: userData.fullName,
        phone_number: userData.phoneNumber,
        role: userData.role,
      });

      // Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: userId,
          email: email,
          full_name: userData.fullName,
          phone_number: userData.phoneNumber,
          role: userData.role,
        });

      if (profileError) {
        console.error('Profile creation error:', profileError);
        // If it's a duplicate key error, that's actually OK - profile already exists
        if ((profileError as any)?.code === '23505') {
          console.log('Profile already exists, continuing...');
        } else {
          throw profileError;
        }
      }

      console.log('User profile created successfully');

      // If user is a biker, create biker profile
      if (userData.role === 'biker') {
        console.log('Creating biker profile for user:', userId);
        const { error: bikerError } = await supabase
          .from('biker_profiles')
          .insert({
            user_id: userId,
            vehicle_type: 'bicycle', // Default
            is_verified: false,
            is_online: false,
            rating: 5.0,
            total_deliveries: 0,
          });

        if (bikerError) {
          console.error('Biker profile creation error:', bikerError);
          throw bikerError;
        }
        console.log('Biker profile created successfully');
      }

      return { error: null };
    } catch (error) {
      return { error };
    }
  },

  // Sign in with email and password
  async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Sign out
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      return { error: null };
    } catch (error) {
      return { error };
    }
  },

  // Get current user
  async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;
      return { user, error: null };
    } catch (error) {
      return { user: null, error };
    }
  },

  // Get user profile
  async getUserProfile(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Update user profile
  async updateUserProfile(userId: string, updates: Partial<UserProfile>) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Mark registration as completed
  async completeRegistration(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .update({ registration_completed: true })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Reset password
  async resetPassword(email: string) {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      if (error) throw error;
      return { error: null };
    } catch (error) {
      return { error };
    }
  },

  // Create profile for existing user (for migration/fix purposes)
  async createProfileForExistingUser(userData: {
    fullName: string;
    phoneNumber: string;
    role: 'shop_owner' | 'client' | 'biker';
  }) {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError || !user) throw userError || new Error('No authenticated user');

      return await this.createUserProfile(user.id, user.email!, userData);
    } catch (error) {
      return { error };
    }
  },
};
