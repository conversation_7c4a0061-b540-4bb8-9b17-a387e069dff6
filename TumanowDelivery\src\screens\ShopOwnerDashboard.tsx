import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing, layout, commonStyles, shadows } from '../theme';
import { useAuth } from '../contexts/AuthContext';

interface ShopOwnerDashboardProps {
  navigation: any;
}

export default function ShopOwnerDashboard({ navigation }: ShopOwnerDashboardProps) {
  const { signOut, userProfile } = useAuth();
  const stats = [
    { title: 'Active Orders', value: '12', icon: 'cube', color: colors.info },
    { title: 'Completed Today', value: '8', icon: 'checkmark-circle', color: colors.success },
    { title: 'Total Revenue', value: '$245', icon: 'cash', color: colors.warning },
    { title: 'Pending Pickup', value: '3', icon: 'time', color: colors.pending },
  ];

  const recentOrders = [
    { id: '1', customer: '<PERSON>', items: '2 items', status: 'pending', time: '10:30 AM' },
    { id: '2', customer: '<PERSON>', items: '1 item', status: 'in_progress', time: '09:45 AM' },
    { id: '3', customer: '<PERSON>', items: '3 items', status: 'delivered', time: '08:20 AM' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return colors.pending;
      case 'in_progress': return colors.inProgress;
      case 'delivered': return colors.delivered;
      default: return colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Pending Pickup';
      case 'in_progress': return 'In Transit';
      case 'delivered': return 'Delivered';
      default: return status;
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: signOut },
      ]
    );
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Good morning!</Text>
            <Text style={styles.shopName}>{userProfile?.full_name || 'My Shop'}</Text>
          </View>
          <TouchableOpacity style={styles.profileButton} onPress={handleLogout}>
            <Ionicons name="log-out" size={24} color={colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          {stats.map((stat, index) => (
            <View key={index} style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                <Ionicons name={stat.icon as any} size={24} color={colors.secondary} />
              </View>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statTitle}>{stat.title}</Text>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionsContainer}>
            <TouchableOpacity style={styles.actionCard}>
              <Ionicons name="add-circle" size={32} color={colors.primary} />
              <Text style={styles.actionText}>New Order</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionCard}>
              <Ionicons name="list" size={32} color={colors.primary} />
              <Text style={styles.actionText}>View All Orders</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionCard}>
              <Ionicons name="settings" size={32} color={colors.primary} />
              <Text style={styles.actionText}>Shop Settings</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Orders */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Orders</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {recentOrders.map((order) => (
            <TouchableOpacity key={order.id} style={styles.orderCard}>
              <View style={styles.orderInfo}>
                <Text style={styles.customerName}>{order.customer}</Text>
                <Text style={styles.orderItems}>{order.items}</Text>
              </View>
              <View style={styles.orderMeta}>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                  <Text style={styles.statusText}>{getStatusText(order.status)}</Text>
                </View>
                <Text style={styles.orderTime}>{order.time}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  greeting: {
    ...typography.body1,
    color: colors.textSecondary,
  },
  shopName: {
    ...typography.h3,
    color: colors.text,
    fontWeight: 'bold',
  },
  profileButton: {
    padding: spacing.xs,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: spacing.xl,
  },
  statCard: {
    backgroundColor: colors.surface,
    borderRadius: layout.cardRadius,
    padding: spacing.md,
    alignItems: 'center',
    width: '48%',
    marginBottom: spacing.md,
    ...shadows.small,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statValue: {
    ...typography.h3,
    color: colors.text,
    fontWeight: 'bold',
  },
  statTitle: {
    ...typography.caption,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    ...typography.h5,
    color: colors.text,
    fontWeight: '600',
  },
  seeAllText: {
    ...typography.body2,
    color: colors.primary,
    fontWeight: '500',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionCard: {
    backgroundColor: colors.surface,
    borderRadius: layout.cardRadius,
    padding: spacing.md,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: spacing.xs,
    ...shadows.small,
  },
  actionText: {
    ...typography.caption,
    color: colors.text,
    marginTop: spacing.sm,
    textAlign: 'center',
    fontWeight: '500',
  },
  orderCard: {
    backgroundColor: colors.surface,
    borderRadius: layout.cardRadius,
    padding: spacing.md,
    marginBottom: spacing.sm,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...shadows.small,
  },
  orderInfo: {
    flex: 1,
  },
  customerName: {
    ...typography.subtitle1,
    color: colors.text,
    fontWeight: '500',
  },
  orderItems: {
    ...typography.body2,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  orderMeta: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: layout.radiusSM,
    marginBottom: spacing.xs,
  },
  statusText: {
    ...typography.caption,
    color: colors.secondary,
    fontWeight: '500',
  },
  orderTime: {
    ...typography.caption,
    color: colors.textSecondary,
  },
});
