{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "tumanow-delivery", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "dark", "sdkVersion": "53.0.0", "splash": {"resizeMode": "contain", "backgroundColor": "#0F0F0F"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.tumanow.delivery"}, "android": {"adaptiveIcon": {"backgroundColor": "#0F0F0F"}, "package": "com.tumanow.delivery", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "RECEIVE_BOOT_COMPLETED", "VIBRATE"]}, "web": {"bundler": "metro"}, "plugins": ["expo-location", "expo-notifications"]}}