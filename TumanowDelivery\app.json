{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "tumanow-delivery", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "splash": {"resizeMode": "contain", "backgroundColor": "#87CEEB"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.tumanow.delivery"}, "android": {"adaptiveIcon": {"backgroundColor": "#87CEEB"}, "package": "com.tumanow.delivery", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "RECEIVE_BOOT_COMPLETED", "VIBRATE"]}, "web": {"bundler": "metro"}, "plugins": ["expo-location", "expo-notifications"]}}