import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing } from '../../../theme';
import { Button } from '../../../components/common';
import { RegistrationData } from '../RegistrationFlowScreen';
import { supabase } from '../../../services/supabase';

interface EmailVerificationStepProps {
  data: RegistrationData;
  updateData: (data: Partial<RegistrationData>) => void;
  onNext: () => void;
  onPrev: () => void;
  onRegister: () => Promise<boolean>;
  loading: boolean;
}

export default function EmailVerificationStep({ 
  data, 
  onNext, 
  onPrev, 
  onRegister, 
  loading 
}: EmailVerificationStepProps) {
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [timeLeft, setTimeLeft] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [registrationComplete, setRegistrationComplete] = useState(false);
  
  const inputRefs = useRef<(TextInput | null)[]>([]);
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Start registration process when component mounts
    handleRegistration();
  }, []);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  useEffect(() => {
    // Pulse animation for the email icon
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulse.start();
    return () => pulse.stop();
  }, []);

  const handleRegistration = async () => {
    const success = await onRegister();
    if (success) {
      setRegistrationComplete(true);
    }
  };

  const handleCodeChange = (value: string, index: number) => {
    if (value.length > 1) return; // Prevent multiple characters
    
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-verify when all digits are entered
    if (newCode.every(digit => digit !== '') && newCode.join('').length === 6) {
      handleVerifyCode(newCode.join(''));
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyCode = async (verificationCode: string) => {
    if (verificationCode.length !== 6) {
      Alert.alert('Invalid Code', 'Please enter the complete 6-digit code');
      return;
    }

    setVerifying(true);
    try {
      const { data: verifyData, error } = await supabase.auth.verifyOtp({
        email: data.email,
        token: verificationCode,
        type: 'signup'
      });

      if (error) {
        Alert.alert('Verification Failed', error.message || 'Invalid verification code');
        // Clear the code
        setCode(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
        return;
      }

      // Success! Now create the user profile
      if (verifyData.user) {
        const { authService } = await import('../../../services/supabase');
        const profileResult = await authService.createUserProfile(
          verifyData.user.id,
          verifyData.user.email!,
          {
            fullName: data.fullName,
            phoneNumber: data.phoneNumber,
            role: data.role,
          }
        );

        if (profileResult.error) {
          Alert.alert('Profile Creation Failed', 'Account verified but failed to create profile. Please contact support.');
          return;
        }
      }

      // Success! Move to next step
      onNext();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Something went wrong');
    } finally {
      setVerifying(false);
    }
  };

  const handleResendCode = async () => {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: data.email,
        options: {
          emailRedirectTo: undefined, // Use OTP instead of email link
        }
      });

      if (error) {
        Alert.alert('Error', error.message || 'Failed to resend code');
        return;
      }

      Alert.alert('Code Sent', 'A new verification code has been sent to your email');
      setTimeLeft(60);
      setCanResend(false);
      setCode(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Something went wrong');
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!registrationComplete) {
    return (
      <View style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <Ionicons name="hourglass" size={32} color={colors.primary} />
            </View>
            <Text style={styles.title}>Creating your account...</Text>
            <Text style={styles.subtitle}>
              Please wait while we set up your Tumanow account
            </Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Animated.View 
            style={[
              styles.iconContainer,
              { transform: [{ scale: pulseAnimation }] }
            ]}
          >
            <Ionicons name="mail" size={32} color={colors.primary} />
          </Animated.View>
          <Text style={styles.title}>Check your email</Text>
          <Text style={styles.subtitle}>
            We've sent a 6-digit verification code to
          </Text>
          <Text style={styles.email}>{data.email}</Text>
        </View>

        {/* Code Input */}
        <View style={styles.codeContainer}>
          <Text style={styles.codeLabel}>Enter verification code</Text>
          <View style={styles.codeInputs}>
            {code.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref) => { inputRefs.current[index] = ref; }}
                style={[
                  styles.codeInput,
                  digit && styles.codeInputFilled,
                ]}
                value={digit}
                onChangeText={(value) => handleCodeChange(value, index)}
                onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                keyboardType="numeric"
                maxLength={1}
                selectTextOnFocus
                autoFocus={index === 0}
              />
            ))}
          </View>
        </View>

        {/* Resend Code */}
        <View style={styles.resendContainer}>
          {canResend ? (
            <TouchableOpacity onPress={handleResendCode} style={styles.resendButton}>
              <Text style={styles.resendText}>Resend Code</Text>
            </TouchableOpacity>
          ) : (
            <Text style={styles.timerText}>
              Resend code in {formatTime(timeLeft)}
            </Text>
          )}
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.buttonRow}>
          <Button
            title="Back"
            onPress={onPrev}
            variant="outline"
            size="large"
            style={styles.backButton}
          />
          <Button
            title="Verify"
            onPress={() => handleVerifyCode(code.join(''))}
            variant="gradient"
            size="large"
            style={styles.verifyButton}
            loading={verifying}
            disabled={code.some(digit => !digit)}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  title: {
    ...typography.h3,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body1,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  email: {
    ...typography.subtitle1,
    color: colors.primary,
    textAlign: 'center',
  },
  codeContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  codeLabel: {
    ...typography.subtitle2,
    color: colors.text,
    marginBottom: spacing.md,
  },
  codeInputs: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  codeInput: {
    width: 48,
    height: 56,
    borderWidth: 2,
    borderColor: colors.border,
    borderRadius: 12,
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    backgroundColor: colors.surface,
  },
  codeInputFilled: {
    borderColor: colors.primary,
    backgroundColor: colors.background,
  },
  resendContainer: {
    alignItems: 'center',
  },
  resendButton: {
    padding: spacing.sm,
  },
  resendText: {
    ...typography.subtitle2,
    color: colors.primary,
  },
  timerText: {
    ...typography.body2,
    color: colors.textSecondary,
  },
  footer: {
    paddingVertical: spacing.lg,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  backButton: {
    flex: 1,
  },
  verifyButton: {
    flex: 2,
  },
});
